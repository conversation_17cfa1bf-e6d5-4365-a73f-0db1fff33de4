<script lang="ts">
  import { onMount } from 'svelte';
  import {
    projectManagementService,
    type ProjectQuery,
    type ProjectWithDetails,
    type ProjectPersonnelWithDetails
  } from '$lib/services/projectManagementService';
  import { Button } from '$lib/components/ui/button';
  import Input from '$lib/components/ui/input.svelte';
  import Table from '$lib/components/ui/table.svelte';
  import ProjectStatsCard from "$lib/components/ProjectStatsCard.svelte";
  import ExportProjectsDialog from "$lib/components/project/ExportProjectsDialog.svelte";

  import { sqliteDictionaryService } from '$lib/services/sqliteDictionaryService';
  import { fileSystemService } from '$lib/services/fileSystemService';
  import { goto } from '$app/navigation';
  import { PlusCircle, Search, Pencil, Trash, UserPlus, RefreshCw, FileDown, Users, Activity, CheckCircle, Folder, ClipboardList } from 'lucide-svelte';
  import { projectFilterStore, saveFilterState } from '$lib/stores/filterStore';
  import { activeCardId, selectedDiseaseStore } from '$lib/stores/cardStore';

  // 状态管理
  let projects = $state<ProjectWithDetails[]>([]);
  let allProjects = $state<ProjectWithDetails[]>([]); // 存储所有项目，用于统计
  let isLoading = $state(false);
  let isDeleting = $state(false);
  let error = $state<string | null>(null);
  let total = $state(0);
  let totalAllProjects = $state(0); // 所有项目的总数
  let currentPage = $state(1);
  let pageSize = $state(50);
  let showDeleteConfirm = $state(false);
  let projectToDelete = $state<{id: string, name: string} | null>(null);
  let confirmProjectName = $state('');
  let deleteConfirmError = $state<string | null>(null);
  let showExportDialog = $state(false); // 控制导出对话框显示

  // 当前活动的过滤器
  let activeFilter = $state<'all' | 'ongoing' | 'finished' | 'recruiting'>('ongoing');

  // 当前选中的疾病（用于二次筛选）
  let selectedDiseaseFilter = $state<string | null>(null);

  // 项目统计
  let projectStats = $state({
    totalProjects: 0,
    ongoingProjects: 0,
    finishedProjects: 0,
    recruitingProjects: 0,
    diseaseDistribution: [] as {disease: string, count: number}[],
    stageDistribution: [] as {stage: string, count: number}[],
    // 各状态下的疾病分布
    allDiseaseDistribution: [] as {disease: string, count: number}[],
    ongoingDiseaseDistribution: [] as {disease: string, count: number}[],
    finishedDiseaseDistribution: [] as {disease: string, count: number}[],
    recruitingDiseaseDistribution: [] as {disease: string, count: number}[]
  });

  // 筛选条件
  let searchName = $state('');
  let selectedDiseaseId = $state<number | null>(null);
  let selectedStageId = $state<number | null>(null);
  let selectedStatusId = $state<number | null>(null);
  let selectedRecruitmentStatusId = $state<number | null>(null);
  let sortBy = $state('project_name');
  let sortOrder = $state<'asc' | 'desc'>('asc');

  // 字典项
  let diseases = $state<{item_id: number, item_value: string}[]>([]);
  let stages = $state<{item_id: number, item_value: string}[]>([]);
  let statuses = $state<{item_id: number, item_value: string}[]>([]);
  let recruitmentStatuses = $state<{item_id: number, item_value: string}[]>([]);

  // 加载所有项目（用于统计）
  async function loadAllProjects() {
    try {
      // 构建查询参数，不包含任何筛选条件
      const query: ProjectQuery = {
        page: 1,
        page_size: 1000, // 设置较大的页面大小以获取所有项目
        sort_by: 'project_name',
        sort_order: 'asc'
      };

      // 调用服务获取所有项目列表
      const result = await projectManagementService.getProjects(query);

      // 获取每个项目的详细信息，包括研究人员和申办方
      const projectsWithDetails = await Promise.all(
        result.items.map(async (project) => {
          if (project.project.project_id) {
            try {
              // 获取项目详情
              const details = await projectManagementService.getProjectDetails(project.project.project_id);
              if (details) {
                // 如果成功获取详情，更新人员信息和申办方信息
                return {
                  ...project,
                  personnel: details.personnel || [],
                  sponsors: details.sponsors || []
                };
              }
            } catch (error) {
              console.error(`获取项目 ${project.project.project_id} 详情失败:`, error);
            }
          }
          return project;
        })
      );

      allProjects = projectsWithDetails;
      totalAllProjects = result.total;

      // 更新项目统计（基于所有项目）
      updateAllProjectStats();
    } catch (err) {
      console.error('加载所有项目失败:', err);
    }
  }

  // 加载筛选后的项目列表
  async function loadProjects() {
    isLoading = true;
    error = null;

    try {
      // 构建查询参数
      const query: ProjectQuery = {
        name: searchName || undefined,
        disease_item_id: selectedDiseaseId || undefined,
        project_stage_item_id: selectedStageId || undefined,
        project_status_item_id: selectedStatusId || undefined,
        recruitment_status_item_id: selectedRecruitmentStatusId || undefined,
        page: currentPage,
        page_size: pageSize,
        sort_by: sortBy,
        sort_order: sortOrder
      };

      // 调用服务获取项目列表
      const result = await projectManagementService.getProjects(query);

      // 获取每个项目的详细信息，包括研究人员和申办方
      const projectsWithDetails = await Promise.all(
        result.items.map(async (project) => {
          if (project.project.project_id) {
            try {
              // 获取项目详情
              const details = await projectManagementService.getProjectDetails(project.project.project_id);
              if (details) {
                // 如果成功获取详情，更新人员信息和申办方信息
                return {
                  ...project,
                  personnel: details.personnel || [],
                  sponsors: details.sponsors || []
                };
              }
            } catch (error) {
              console.error(`获取项目 ${project.project.project_id} 详情失败:`, error);
            }
          }
          return project;
        })
      );

      projects = projectsWithDetails;
      total = result.total;
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
    } finally {
      isLoading = false;
    }
  }

  // 更新所有项目的统计信息（基于allProjects）
  function updateAllProjectStats() {
    if (!allProjects || allProjects.length === 0) return;

    // 计算基本统计
    projectStats.totalProjects = totalAllProjects;

    // 计算在研项目数量（项目状态为"在研"的项目）
    const ongoingProjects = allProjects.filter(p =>
      p.project_status?.item_value === '在研'
    );
    projectStats.ongoingProjects = ongoingProjects.length;

    // 计算已结束项目数量（项目状态为"已结束"的项目）
    const finishedProjects = allProjects.filter(p =>
      p.project_status?.item_value === '已结束'
    );
    projectStats.finishedProjects = finishedProjects.length;

    // 计算招募中项目数量
    const recruitingProjects = allProjects.filter(p =>
      p.recruitment_status?.item_value === '招募中'
    );
    projectStats.recruitingProjects = recruitingProjects.length;

    // 计算所有项目的疾病分布
    const diseaseMap = new Map<string, number>();
    allProjects.forEach(p => {
      if (p.disease?.item_value) {
        const count = diseaseMap.get(p.disease.item_value) || 0;
        diseaseMap.set(p.disease.item_value, count + 1);
      }
    });
    projectStats.diseaseDistribution = Array.from(diseaseMap.entries())
      .map(([disease, count]) => ({ disease, count }))
      .sort((a, b) => b.count - a.count);

    // 计算所有项目的疾病分布（用于"总项目数"过滤器）
    projectStats.allDiseaseDistribution = [...projectStats.diseaseDistribution];

    // 计算在研项目的疾病分布
    const ongoingDiseaseMap = new Map<string, number>();
    ongoingProjects.forEach(p => {
      if (p.disease?.item_value) {
        const count = ongoingDiseaseMap.get(p.disease.item_value) || 0;
        ongoingDiseaseMap.set(p.disease.item_value, count + 1);
      }
    });
    projectStats.ongoingDiseaseDistribution = Array.from(ongoingDiseaseMap.entries())
      .map(([disease, count]) => ({ disease, count }))
      .sort((a, b) => b.count - a.count);

    // 计算已结束项目的疾病分布
    const finishedDiseaseMap = new Map<string, number>();
    finishedProjects.forEach(p => {
      if (p.disease?.item_value) {
        const count = finishedDiseaseMap.get(p.disease.item_value) || 0;
        finishedDiseaseMap.set(p.disease.item_value, count + 1);
      }
    });
    projectStats.finishedDiseaseDistribution = Array.from(finishedDiseaseMap.entries())
      .map(([disease, count]) => ({ disease, count }))
      .sort((a, b) => b.count - a.count);

    // 计算招募中项目的疾病分布
    const recruitingDiseaseMap = new Map<string, number>();
    recruitingProjects.forEach(p => {
      if (p.disease?.item_value) {
        const count = recruitingDiseaseMap.get(p.disease.item_value) || 0;
        recruitingDiseaseMap.set(p.disease.item_value, count + 1);
      }
    });
    projectStats.recruitingDiseaseDistribution = Array.from(recruitingDiseaseMap.entries())
      .map(([disease, count]) => ({ disease, count }))
      .sort((a, b) => b.count - a.count);

    // 计算阶段分布
    const stageMap = new Map<string, number>();
    allProjects.forEach(p => {
      if (p.project_stage?.item_value) {
        const count = stageMap.get(p.project_stage.item_value) || 0;
        stageMap.set(p.project_stage.item_value, count + 1);
      }
    });
    projectStats.stageDistribution = Array.from(stageMap.entries())
      .map(([stage, count]) => ({ stage, count }))
      .sort((a, b) => b.count - a.count);
  }

  // 加载字典项
  async function loadDictionaryItems() {
    try {
      // 加载疾病字典
      const diseasesDict = await sqliteDictionaryService.getDictByName('疾病');
      console.log('疾病字典:', diseasesDict);
      if (diseasesDict && diseasesDict.items) {
        diseases = diseasesDict.items.map(item => ({
          item_id: item.item_id || 0,
          item_value: item.value
        }));
        console.log('加载的疾病列表:', diseases);
      }

      // 加载项目阶段字典
      console.log('开始加载研究分期字典...');
      try {
        const stagesDict = await sqliteDictionaryService.getDictByName('研究分期');
        console.log('研究分期字典详情:', JSON.stringify(stagesDict, null, 2));

        if (stagesDict && stagesDict.items) {
          console.log('研究分期字典项数量:', stagesDict.items.length);
          console.log('研究分期字典项示例:', stagesDict.items.slice(0, 3));

          stages = stagesDict.items.map(item => ({
            item_id: item.item_id || 0,
            item_value: item.value
          }));
          console.log('加载的研究分期列表:', stages);
        } else {
          console.error('研究分期字典为空或没有items属性');
        }
      } catch (err) {
        console.error('加载研究分期字典时出错:', err);
      }

      // 加载项目状态字典（使用研究阶段字典）
      console.log('开始加载项目状态字典...');
      try {
        // 直接加载"研究阶段"字典
        const statusesDict = await sqliteDictionaryService.getDictByName('研究阶段');
        console.log('研究阶段字典详情:', JSON.stringify(statusesDict, null, 2));

        if (statusesDict && statusesDict.items) {
          console.log('研究阶段字典项数量:', statusesDict.items.length);
          console.log('研究阶段字典项示例:', statusesDict.items.slice(0, 3));

          statuses = statusesDict.items.map(item => ({
            item_id: item.item_id || 0,
            item_value: item.value
          }));
          console.log('加载的研究阶段列表:', statuses);
        } else {
          console.error('研究阶段字典为空或没有items属性');
          // 如果无法加载，则使用空数组
          statuses = [];
        }
      } catch (err) {
        console.error('加载研究阶段字典时出错:', err);
        // 出错时使用空数组
        statuses = [];
      }

      // 加载招募状态字典
      console.log('开始加载招募状态字典...');
      try {
        // 首先尝试加载"招募状态"字典
        let recruitmentStatusesDict = await sqliteDictionaryService.getDictByName('招募状态');
        console.log('招募状态字典详情:', JSON.stringify(recruitmentStatusesDict, null, 2));

        // 如果"招募状态"字典不存在或为空，则尝试加载"招募公司"字典作为备用
        if (!recruitmentStatusesDict || !recruitmentStatusesDict.items || recruitmentStatusesDict.items.length === 0) {
          console.log('招募状态字典为空，尝试加载招募公司字典作为备用...');
          recruitmentStatusesDict = await sqliteDictionaryService.getDictByName('招募公司');
          console.log('招募公司字典详情(作为招募状态备用):', JSON.stringify(recruitmentStatusesDict, null, 2));
        }

        if (recruitmentStatusesDict && recruitmentStatusesDict.items) {
          console.log('招募状态字典项数量:', recruitmentStatusesDict.items.length);
          console.log('招募状态字典项示例:', recruitmentStatusesDict.items.slice(0, 3));

          recruitmentStatuses = recruitmentStatusesDict.items.map(item => ({
            item_id: item.item_id || 0,
            item_value: item.value
          }));
          console.log('加载的招募状态列表:', recruitmentStatuses);
        } else {
          console.error('招募状态字典为空或没有items属性');
          // 如果仍然无法加载，则使用空数组
          recruitmentStatuses = [];
        }
      } catch (err) {
        console.error('加载招募状态字典时出错:', err);
        // 出错时使用空数组
        recruitmentStatuses = [];
      }
    } catch (err) {
      console.error('加载字典项失败:', err);
    }
  }

  // 初始化项目管理表
  async function initProjectTables() {
    try {
      await projectManagementService.initTables();
      console.log('项目管理表初始化成功');
    } catch (err) {
      console.error('项目管理表初始化失败:', err);
    }
  }

  // 处理搜索
  function handleSearch() {
    currentPage = 1;
    loadProjects();
  }

  // 处理状态卡片点击
  function handleStatusCardClick(filter: 'all' | 'ongoing' | 'finished' | 'recruiting') {
    // 更新当前活动的过滤器
    activeFilter = filter;

    // 重置疾病二次筛选
    selectedDiseaseFilter = null;

    // 重置页码
    currentPage = 1;

    // 根据过滤器设置相应的状态ID
    switch (filter) {
      case 'all':
        // 显示所有项目，清除状态过滤
        selectedStatusId = null;
        selectedRecruitmentStatusId = null;
        break;
      case 'ongoing':
        // 查找"在研"状态的项目ID
        const ongoingStatus = statuses.find(s => s.item_value === '在研');
        selectedStatusId = ongoingStatus?.item_id || null;
        selectedRecruitmentStatusId = null;
        break;
      case 'finished':
        // 查找"已结束"状态的项目ID
        const finishedStatus = statuses.find(s => s.item_value === '已结束');
        selectedStatusId = finishedStatus?.item_id || null;
        selectedRecruitmentStatusId = null;
        break;
      case 'recruiting':
        // 查找"招募中"状态的项目ID
        const recruitingStatus = recruitmentStatuses.find(s => s.item_value === '招募中');
        selectedStatusId = null; // 清除研究阶段过滤
        selectedRecruitmentStatusId = recruitingStatus?.item_id || null;
        break;
    }

    // 加载过滤后的项目（不更新统计数据）
    loadProjects();
  }

  // 处理疾病筛选点击
  function handleDiseaseFilterClick(diseaseName: string) {
    // 如果点击的是当前已选中的疾病，则取消选择
    if (selectedDiseaseFilter === diseaseName) {
      selectedDiseaseFilter = null;
      // 清除疾病筛选条件
      selectedDiseaseId = null;
    } else {
      // 否则设置为新选中的疾病
      selectedDiseaseFilter = diseaseName;
      // 查找对应的疾病ID
      const diseaseItem = diseases.find(d => d.item_value === diseaseName);
      selectedDiseaseId = diseaseItem?.item_id || null;
    }

    // 重置页码
    currentPage = 1;

    // 加载过滤后的项目
    loadProjects();
  }

  // 处理排序
  function handleSort(column: string) {
    if (sortBy === column) {
      // 如果已经按此列排序，则切换排序方向
      sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      // 否则按此列升序排序
      sortBy = column;
      sortOrder = 'asc';
    }
    loadProjects();
  }

  // 获取排序图标
  function getSortIcon(column: string) {
    if (sortBy !== column) return '';
    return sortOrder === 'asc' ? '↑' : '↓';
  }

  // 按角色分组人员
  function groupPersonnelByRole(personnel: ProjectPersonnelWithDetails[]) {
    if (!personnel || personnel.length === 0) return [];

    const roleGroups: {
      roleId: number;
      roleName: string;
      personnel: Array<{
        index: number;
        person: ProjectPersonnelWithDetails;
      }>;
    }[] = [];

    // 遍历所有人员，按角色分组
    personnel.forEach((person, index) => {
      const roleId = person.role_item_id;
      const roleName = person.role?.item_value || '未知角色';

      // 查找该角色是否已存在于分组中
      let roleGroup = roleGroups.find(group => group.roleId === roleId);

      if (!roleGroup) {
        // 如果角色不存在，创建新的角色分组
        roleGroup = {
          roleId,
          roleName,
          personnel: []
        };
        roleGroups.push(roleGroup);
      }

      // 将人员添加到对应角色分组
      roleGroup.personnel.push({
        index,
        person
      });
    });

    // 按角色名称排序
    roleGroups.sort((a, b) => a.roleName.localeCompare(b.roleName));

    return roleGroups;
  }

  // 导出项目数据到CSV
  function exportProjectsToCSV() {
    if (!projects || projects.length === 0) {
      alert('没有可导出的数据');
      return;
    }

    // 准备CSV标题行
    const headers = [
      '项目简称',
      '项目全称',
      '疾病',
      '申办方',
      '研究分期',
      '项目状态',
      '招募状态',
      '研究人员',
      '启动日期/已启动天数'
    ];

    // 准备CSV数据行
    const rows = projects.map(project => {
      // 处理研究人员信息
      let personnelInfo = '';
      if (project.personnel && project.personnel.length > 0) {
        const roleGroups = groupPersonnelByRole(project.personnel);
        personnelInfo = roleGroups.map(group =>
          `${group.roleName}: ${group.personnel.map(p => p.person.personnel?.name || '未知').join('、')}`
        ).join('; ');
      }

      // 计算已启动天数
      const days = calculateDaysSinceStart(project.project.project_start_date);
      const daysText = days !== null ? `${days}天` : '';

      // 合并启动日期和已启动天数
      const startDateInfo = project.project.project_start_date
        ? `${project.project.project_start_date} (${daysText})`
        : '';

      // 处理申办方信息
      let sponsorInfo = '';
      if (project.sponsors && project.sponsors.length > 0) {
        sponsorInfo = project.sponsors
          .map(s => s.sponsor?.item_value || '')
          .filter(s => s)
          .join('、');
      }

      return [
        project.project.project_short_name || '',
        project.project.project_name || '',
        project.disease?.item_value || '',
        sponsorInfo,
        project.project_stage?.item_value || '',
        project.project_status?.item_value || '',
        project.recruitment_status?.item_value || '',
        personnelInfo,
        startDateInfo
      ];
    });

    // 组合CSV内容
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.map(cell => `"${cell.replace(/"/g, '""')}"`).join(','))
    ].join('\n');

    // 创建Blob对象
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

    // 创建下载链接
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    // 设置下载属性
    link.setAttribute('href', url);
    link.setAttribute('download', `项目列表_${new Date().toISOString().slice(0, 10)}.csv`);
    link.style.visibility = 'hidden';

    // 添加到文档并触发点击
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  // 处理页码变化
  function handlePageChange(page: number) {
    currentPage = page;
    loadProjects();
  }

  // 处理查看项目
  function handleViewProject(projectId: string) {
    // 获取当前活动卡片ID
    let currentActiveCardId: string | null = null;
    const unsubscribeCardId = activeCardId.subscribe(id => {
      currentActiveCardId = id;
    });
    unsubscribeCardId();

    console.log('Saving filter state with active card ID:', currentActiveCardId);
    console.log('Selected disease filter:', selectedDiseaseFilter);

    // 保存当前的筛选状态到 store
    saveFilterState({
      activeFilter,
      selectedDiseaseFilter,
      searchName,
      sortBy,
      sortOrder: sortOrder as 'asc' | 'desc',
      currentPage,
      selectedDiseaseId,
      selectedStageId,
      selectedStatusId,
      selectedRecruitmentStatusId,
      activeCardId: currentActiveCardId
    });

    goto(`/projects/${projectId}`);
  }

  // 处理编辑项目
  function handleEditProject(projectId: string) {
    console.log('编辑项目，项目ID:', projectId);
    if (!projectId) {
      console.error('无效的项目ID');
      error = '无效的项目ID';
      return;
    }
    goto(`/projects/${projectId}/edit`);
  }

  // 处理删除项目
  function handleDeleteProject(projectId: string) {
    // 查找要删除的项目
    const project = projects.find(p => p.project.project_id === projectId);
    if (!project) {
      error = "找不到要删除的项目";
      return;
    }

    // 设置要删除的项目信息
    projectToDelete = {
      id: projectId,
      name: project.project.project_short_name || project.project.project_name
    };

    // 显示确认对话框
    showDeleteConfirm = true;
  }

  // 确认删除项目
  async function confirmDeleteProject() {
    if (!projectToDelete) return;

    // 验证输入的项目简称是否匹配
    if (confirmProjectName !== projectToDelete.name) {
      deleteConfirmError = "输入的项目简称不匹配，请重新输入";
      return;
    }

    isDeleting = true;
    error = null;
    deleteConfirmError = null;

    try {
      await projectManagementService.deleteProject(projectToDelete.id);
      showDeleteConfirm = false;
      projectToDelete = null;
      confirmProjectName = '';

      // 删除项目后，重新加载所有项目以更新统计数据
      await loadAllProjects();
      // 然后加载当前筛选的项目列表
      loadProjects();
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
    } finally {
      isDeleting = false;
    }
  }

  // 取消删除项目
  function cancelDeleteProject() {
    showDeleteConfirm = false;
    projectToDelete = null;
    confirmProjectName = '';
    deleteConfirmError = null;
  }

  // 计算已启动天数
  function calculateDaysSinceStart(startDate: string | undefined): number | null {
    if (!startDate) return null;
    try {
      const start = new Date(startDate);
      const now = new Date();
      // 检查日期是否有效
      if (isNaN(start.getTime())) {
        return null;
      }
      // 计算天数差
      const diffTime = now.getTime() - start.getTime();
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
      return diffDays;
    } catch (error) {
      console.error('计算启动天数时出错:', error);
      return null;
    }
  }

  // 处理打开项目文件夹
  async function handleOpenProjectFolder(projectPath: string | undefined) {
    if (!projectPath) {
      error = "项目路径不存在";
      return;
    }

    try {
      const response = await fileSystemService.openFolder(projectPath);
      if (!response.success) {
        error = response.error || "打开项目文件夹失败";
      }
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
    }
  }

  // 处理配置入排标准
  function handleConfigureCriteria(projectId: string) {
    if (!projectId) {
      console.error('无效的项目ID');
      error = '无效的项目ID';
      return;
    }
    goto(`/projects/${projectId}/criteria`);
  }

  // 组件挂载时初始化
  onMount(async () => {
    await initProjectTables();
    await loadDictionaryItems();

    // 首先加载所有项目以获取统计数据
    await loadAllProjects();

    // 从 store 中获取保存的筛选状态
    let filterState: any;
    const unsubscribe = projectFilterStore.subscribe(state => {
      filterState = state;
    });
    unsubscribe();

    if (filterState) {
      // 恢复筛选状态
      activeFilter = filterState.activeFilter;
      selectedDiseaseFilter = filterState.selectedDiseaseFilter;
      searchName = filterState.searchName;
      sortBy = filterState.sortBy;
      sortOrder = filterState.sortOrder as 'asc' | 'desc';
      currentPage = filterState.currentPage;
      selectedDiseaseId = filterState.selectedDiseaseId;
      selectedStageId = filterState.selectedStageId;
      selectedStatusId = filterState.selectedStatusId;
      selectedRecruitmentStatusId = filterState.selectedRecruitmentStatusId;

      // 恢复卡片翻转状态和疾病筛选状态
      if (filterState.activeCardId) {
        console.log('Restoring active card ID:', filterState.activeCardId);
        // 设置活动卡片ID，这会使卡片翻转
        activeCardId.set(filterState.activeCardId);

        // 如果有选中的疾病，也需要恢复疾病筛选状态
        if (filterState.selectedDiseaseFilter) {
          console.log('Restoring selected disease:', filterState.selectedDiseaseFilter);
          selectedDiseaseStore.set(filterState.selectedDiseaseFilter);
        }
      } else {
        console.log('No active card ID to restore');
      }

      // 加载筛选后的项目
      loadProjects();
    } else {
      // 默认应用"在研项目"过滤器
      handleStatusCardClick('ongoing');
    }
  });
</script>

<div class="container mx-auto px-4 py-8">
  <div class="flex justify-between items-center mb-6 bg-white p-4 rounded-lg shadow">
    <div class="flex items-center">
      <div class="bg-blue-600 p-2 rounded-lg mr-3">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      </div>
      <div>
        <h1 class="text-2xl font-bold text-gray-800">项目管理</h1>
        <p class="text-sm text-gray-500">管理研究项目、人员和申办方</p>
      </div>
    </div>
    <div class="flex items-center gap-3">
      <!-- 搜索项目名称 -->
      <div class="flex items-center gap-2">
        <div class="relative w-64">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search class="h-4 w-4 text-gray-400" />
          </div>
          <Input
            type="text"
            placeholder="搜索项目名称..."
            bind:value={searchName}
            class="pl-9"
            onkeydown={(e: KeyboardEvent) => e.key === 'Enter' && handleSearch()}
          />
          {#if searchName}
            <button
              class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
              onclick={() => { searchName = ''; handleSearch(); }}
              aria-label="清除搜索"
              title="清除搜索"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          {/if}
        </div>
        <Button
          variant="outline"
          size="sm"
          onclick={handleSearch}
          class="h-10 px-3"
        >
          搜索
        </Button>
      </div>
      <div class="flex gap-2">
        <Button variant="outline" onclick={async () => {
          await loadAllProjects(); // 先更新统计数据
          loadProjects(); // 然后加载当前筛选的项目
        }} class="border-gray-300 hover:bg-gray-50">
          <RefreshCw class="mr-2 h-4 w-4" />
          刷新
        </Button>
        <Button variant="outline" onclick={() => showExportDialog = true} class="border-gray-300 hover:bg-gray-50">
          <FileDown class="mr-2 h-4 w-4" />
          导出
        </Button>
        <Button onclick={() => goto('/projects/new')} class="bg-blue-600 hover:bg-blue-700">
          <PlusCircle class="mr-2 h-4 w-4" />
          新建项目
        </Button>
      </div>
    </div>
  </div>

  <!-- 项目统计仪表板 -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 h-[220px]">
    <!-- 总项目数卡片 -->
    <ProjectStatsCard
      id="all-projects-card"
      title="总项目数"
      count={projectStats.totalProjects}
      icon={Users}
      color="blue"
      isActive={activeFilter === 'all'}
      diseaseDistribution={projectStats.allDiseaseDistribution}
      selectedDisease={selectedDiseaseFilter}
      onClick={() => handleStatusCardClick('all')}
      onDiseaseClick={handleDiseaseFilterClick}
    />

    <!-- 在研项目卡片 -->
    <ProjectStatsCard
      id="ongoing-projects-card"
      title="在研项目"
      count={projectStats.ongoingProjects}
      icon={Activity}
      color="green"
      isActive={activeFilter === 'ongoing'}
      diseaseDistribution={projectStats.ongoingDiseaseDistribution}
      selectedDisease={selectedDiseaseFilter}
      onClick={() => handleStatusCardClick('ongoing')}
      onDiseaseClick={handleDiseaseFilterClick}
    />

    <!-- 已结束项目卡片 -->
    <ProjectStatsCard
      id="finished-projects-card"
      title="已结束项目"
      count={projectStats.finishedProjects}
      icon={CheckCircle}
      color="purple"
      isActive={activeFilter === 'finished'}
      diseaseDistribution={projectStats.finishedDiseaseDistribution}
      selectedDisease={selectedDiseaseFilter}
      onClick={() => handleStatusCardClick('finished')}
      onDiseaseClick={handleDiseaseFilterClick}
    />

    <!-- 招募中项目卡片 -->
    <ProjectStatsCard
      id="recruiting-projects-card"
      title="招募中项目"
      count={projectStats.recruitingProjects}
      icon={UserPlus}
      color="orange"
      isActive={activeFilter === 'recruiting'}
      diseaseDistribution={projectStats.recruitingDiseaseDistribution}
      selectedDisease={selectedDiseaseFilter}
      onClick={() => handleStatusCardClick('recruiting')}
      onDiseaseClick={handleDiseaseFilterClick}
    />
  </div>

  <!-- 错误提示 -->
  {#if error}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      <p>{error}</p>
    </div>
  {/if}

  <!-- 项目列表 -->
  <div class="bg-white rounded-lg shadow overflow-hidden overflow-x-auto">
    <div class="min-w-[1000px] w-full">
      <Table>
      <thead>
        <tr class="bg-gray-50 border-b border-gray-200">
          <th class="cursor-pointer w-[5%] text-center" title="项目状态">
            状态
          </th>
          <th class="cursor-pointer w-[15%]" onclick={() => handleSort('project_short_name')}>
            <div class="flex items-center gap-1">
              <span>项目简称</span>
              <span class="text-gray-500">{getSortIcon('project_short_name')}</span>
            </div>
          </th>
          <th class="cursor-pointer w-[10%]" onclick={() => handleSort('disease_item_id')}>
            <div class="flex items-center gap-1">
              <span>疾病</span>
              <span class="text-gray-500">{getSortIcon('disease_item_id')}</span>
            </div>
          </th>
          <th class="cursor-pointer w-[12%]" onclick={() => handleSort('sponsor_item_id')}>
            <div class="flex items-center gap-1">
              <span>申办方</span>
              <span class="text-gray-500">{getSortIcon('sponsor_item_id')}</span>
            </div>
          </th>
          <th class="cursor-pointer w-[8%]" onclick={() => handleSort('project_stage_item_id')}>
            <div class="flex items-center gap-1">
              <span>研究分期</span>
              <span class="text-gray-500">{getSortIcon('project_stage_item_id')}</span>
            </div>
          </th>
          <th class="cursor-pointer w-[8%]" onclick={() => handleSort('project_status_item_id')}>
            <div class="flex items-center gap-1">
              <span>研究阶段</span>
              <span class="text-gray-500">{getSortIcon('project_status_item_id')}</span>
            </div>
          </th>
          <th class="cursor-pointer w-[8%]" onclick={() => handleSort('recruitment_status_item_id')}>
            <div class="flex items-center gap-1">
              <span>招募状态</span>
              <span class="text-gray-500">{getSortIcon('recruitment_status_item_id')}</span>
            </div>
          </th>
          <th class="w-[20%]">研究人员</th>
          <th class="cursor-pointer w-[10%]" onclick={() => handleSort('project_start_date')}>
            <div class="flex items-center gap-1">
              <span>已启动</span>
              <span class="text-gray-500">{getSortIcon('project_start_date')}</span>
            </div>
          </th>
          <th class="text-center w-[12%]">操作</th>
        </tr>
      </thead>
      <tbody>
        {#if isLoading}
          <tr>
            <td colspan="10" class="text-center py-8">
              <div class="flex justify-center items-center">
                <div class="animate-spin h-5 w-5 mr-3 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                加载中...
              </div>
            </td>
          </tr>
        {:else if projects.length === 0}
          <tr>
            <td colspan="10" class="text-center py-8">
              <div class="flex flex-col items-center justify-center text-gray-500">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <p>暂无项目数据</p>
              </div>
            </td>
          </tr>
        {:else}
          {#each projects as project, index}
            <tr
              class="cursor-pointer hover:bg-blue-50 transition-colors duration-150 ease-in-out {index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}"
              onclick={() => handleViewProject(project.project.project_id || '')}
            >
              <!-- 状态指示器 -->
              <td class="text-center py-3">
                <div class="flex flex-col items-center justify-center gap-1">
                  <!-- 研究状态指示器 -->
                  <div
                    class="w-3 h-3 rounded-full border border-gray-200"
                    class:bg-green-500={project.project_status?.item_value === '在研'}
                    class:bg-gray-500={project.project_status?.item_value === '已结束'}
                    class:bg-yellow-500={project.project_status?.item_value === '准备中'}
                    class:bg-red-500={project.project_status?.item_value === '暂停'}
                    class:bg-blue-500={!project.project_status?.item_value}
                    title={`研究状态: ${project.project_status?.item_value || '未设置'}`}
                  ></div>
                  <!-- 招募状态指示器 -->
                  <div
                    class="w-3 h-3 transform rotate-45 border border-gray-200"
                    class:bg-orange-500={project.recruitment_status?.item_value === '招募中'}
                    class:bg-green-500={project.recruitment_status?.item_value === '招募完成'}
                    class:bg-gray-500={project.recruitment_status?.item_value === '未招募'}
                    class:bg-blue-500={!project.recruitment_status?.item_value}
                    title={`招募状态: ${project.recruitment_status?.item_value || '未设置'}`}
                  ></div>
                </div>
              </td>

              <!-- 项目简称 -->
              <td class="py-3">
                <div class="flex items-center gap-2">
                  <div class="flex-shrink-0 text-blue-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div class="truncate font-medium" title={project.project.project_short_name}>
                    {project.project.project_short_name}
                  </div>
                </div>
              </td>

              <!-- 疾病 -->
              <td class="py-3">
                <div class="px-2 py-1 bg-blue-50 text-blue-700 rounded-md text-xs inline-block max-w-full truncate" title={project.disease?.item_value || '-'}>
                  {project.disease?.item_value || '-'}
                </div>
              </td>

              <!-- 申办方 -->
              <td class="py-3">
                {#if project.sponsors && project.sponsors.length > 0}
                  <div class="flex flex-wrap gap-1">
                    {#each project.sponsors as sponsor, i}
                      {#if i < 2}
                        <div class="px-2 py-1 bg-purple-50 text-purple-700 rounded-md text-xs inline-block truncate max-w-[120px]" title={sponsor.sponsor?.item_value || '-'}>
                          {sponsor.sponsor?.item_value || '-'}
                        </div>
                        {#if i < project.sponsors.length - 1 && i < 1}
                          <span class="text-gray-400 mx-0.5">+</span>
                        {/if}
                      {:else if i === 2}
                        <div class="px-2 py-1 bg-gray-100 text-gray-600 rounded-md text-xs inline-block">
                          +{project.sponsors.length - 2}
                        </div>
                      {/if}
                    {/each}
                  </div>
                {:else}
                  <span class="text-gray-400 text-sm">-</span>
                {/if}
              </td>

              <!-- 研究分期 -->
              <td class="py-3">
                <div class="px-2 py-1 bg-green-50 text-green-700 rounded-md text-xs inline-block max-w-full truncate" title={project.project_stage?.item_value || '-'}>
                  {project.project_stage?.item_value || '-'}
                </div>
              </td>

              <!-- 研究阶段 -->
              <td class="py-3">
                <div
                  class="px-2 py-1 rounded-md text-xs inline-block max-w-full truncate"
                  class:bg-green-50={project.project_status?.item_value === '在研'}
                  class:text-green-700={project.project_status?.item_value === '在研'}
                  class:bg-gray-50={project.project_status?.item_value === '已结束'}
                  class:text-gray-700={project.project_status?.item_value === '已结束'}
                  class:bg-yellow-50={project.project_status?.item_value === '准备中'}
                  class:text-yellow-700={project.project_status?.item_value === '准备中'}
                  class:bg-red-50={project.project_status?.item_value === '暂停'}
                  class:text-red-700={project.project_status?.item_value === '暂停'}
                  class:bg-blue-50={!project.project_status?.item_value}
                  class:text-blue-700={!project.project_status?.item_value}
                  title={project.project_status?.item_value || '-'}
                >
                  {project.project_status?.item_value || '-'}
                </div>
              </td>

              <!-- 招募状态 -->
              <td class="py-3">
                <div
                  class="px-2 py-1 rounded-md text-xs inline-block max-w-full truncate"
                  class:bg-orange-50={project.recruitment_status?.item_value === '招募中'}
                  class:text-orange-700={project.recruitment_status?.item_value === '招募中'}
                  class:bg-green-50={project.recruitment_status?.item_value === '招募完成'}
                  class:text-green-700={project.recruitment_status?.item_value === '招募完成'}
                  class:bg-gray-50={project.recruitment_status?.item_value === '未招募'}
                  class:text-gray-700={project.recruitment_status?.item_value === '未招募'}
                  class:bg-blue-50={!project.recruitment_status?.item_value}
                  class:text-blue-700={!project.recruitment_status?.item_value}
                  title={project.recruitment_status?.item_value || '-'}
                >
                  {project.recruitment_status?.item_value || '-'}
                </div>
              </td>

              <!-- 研究人员 -->
              <td class="py-3">
                {#if project.personnel && project.personnel.length > 0}
                  <div class="flex flex-wrap gap-1.5">
                    {#each groupPersonnelByRole(project.personnel) as roleGroup, i}
                      {#if i < 3}
                        <div class="group relative">
                          <div class="flex items-center gap-1 bg-gray-100 px-2 py-1 rounded-md hover:bg-gray-200 transition-colors">
                            <span class="text-xs font-medium text-gray-700">{roleGroup.roleName}</span>
                            <span class="text-xs text-gray-500">{roleGroup.personnel.length}</span>
                          </div>
                          <!-- 悬浮显示详细人员 -->
                          <div class="absolute left-0 top-full mt-1 z-10 bg-white shadow-lg rounded-md p-2 w-48 hidden group-hover:block">
                            <div class="text-xs font-medium text-gray-700 mb-1 pb-1 border-b">{roleGroup.roleName}</div>
                            <div class="flex flex-col gap-1">
                              {#each roleGroup.personnel as p}
                                <div class="text-xs text-gray-600">{p.person.personnel?.name || '未知'}</div>
                              {/each}
                            </div>
                          </div>
                        </div>
                      {:else if i === 3}
                        <div class="bg-gray-100 px-2 py-1 rounded-md text-xs text-gray-500">
                          +{groupPersonnelByRole(project.personnel).length - 3}
                        </div>
                      {/if}
                    {/each}
                  </div>
                {:else}
                  <span class="text-gray-400 text-xs">暂无人员</span>
                {/if}
              </td>

              <!-- 已启动 -->
              <td class="py-3">
                {#if project.project.project_start_date}
                  {@const days = calculateDaysSinceStart(project.project.project_start_date)}
                  <div class="flex items-center gap-2">
                    <div
                      class="text-base font-medium tabular-nums"
                      class:text-green-600={days !== null && days > 180}
                      class:text-blue-600={days !== null && days > 90 && days <= 180}
                      class:text-orange-500={days !== null && days <= 90}
                    >
                      {days !== null ? days : '-'}
                    </div>
                    <div class="flex flex-col">
                      <span class="text-xs text-gray-500">天</span>
                      <span class="text-xs text-gray-400" title={project.project.project_start_date}>
                        {project.project.project_start_date.split('T')[0].replace(/-/g, '/')}
                      </span>
                    </div>
                  </div>
                {:else}
                  <span class="text-gray-400">-</span>
                {/if}
              </td>

              <!-- 操作 -->
              <td class="text-center py-3" onclick={(e) => e.stopPropagation()}>
                <div class="flex justify-center items-center gap-1">
                  <!-- 编辑按钮 -->
                  <Button
                    variant="ghost"
                    size="icon"
                    class="h-8 w-8 p-0 text-gray-600 hover:text-blue-600 hover:bg-blue-50"
                    title="编辑项目"
                    onclick={() => handleEditProject(project.project.project_id || '')}
                  >
                    <Pencil class="h-4 w-4" />
                  </Button>

                  <!-- 配置入排标准按钮 -->
                  <Button
                    variant="ghost"
                    size="icon"
                    class="h-8 w-8 p-0 text-gray-600 hover:text-purple-600 hover:bg-purple-50"
                    title="配置入排标准"
                    onclick={() => handleConfigureCriteria(project.project.project_id || '')}
                  >
                    <ClipboardList class="h-4 w-4" />
                  </Button>

                  <!-- 文件夹按钮 -->
                  <Button
                    variant="ghost"
                    size="icon"
                    class="h-8 w-8 p-0 text-gray-600 hover:text-green-600 hover:bg-green-50"
                    title="打开项目文件夹"
                    onclick={() => handleOpenProjectFolder(project.project.project_path)}
                  >
                    <Folder class="h-4 w-4" />
                  </Button>

                  <!-- 删除按钮 -->
                  <Button
                    variant="ghost"
                    size="icon"
                    class="h-8 w-8 p-0 text-gray-600 hover:text-red-600 hover:bg-red-50"
                    title="删除项目"
                    onclick={() => handleDeleteProject(project.project.project_id || '')}
                  >
                    <Trash class="h-4 w-4" />
                  </Button>
                </div>
              </td>
            </tr>
          {/each}
        {/if}
      </tbody>
    </Table>
    </div>
  </div>

  <!-- 分页 -->
  {#if total > 0}
    <div class="flex justify-between items-center mt-4 bg-white p-3 rounded-lg shadow">
      <div class="flex items-center gap-2">
        <span class="text-sm text-gray-600">每页显示:</span>
        <select
          bind:value={pageSize}
          onchange={() => { currentPage = 1; loadProjects(); }}
          class="h-8 w-20 rounded-md border border-input bg-background px-3 py-1 text-sm"
        >
          {#each [5, 10, 20, 50, 100] as size}
            <option value={size}>{size}</option>
          {/each}
        </select>
        <span class="text-sm text-gray-600">共 <span class="font-medium text-gray-800">{total}</span> 条记录，当前第 <span class="font-medium text-gray-800">{currentPage}</span> 页</span>
      </div>
      <div class="flex gap-1">
        <Button
          variant="outline"
          size="sm"
          disabled={currentPage === 1}
          on:click={() => handlePageChange(1)}
          class="h-8 px-2 min-w-[40px]"
        >
          首页
        </Button>
        <Button
          variant="outline"
          size="sm"
          disabled={currentPage === 1}
          on:click={() => handlePageChange(currentPage - 1)}
          class="h-8 px-2 min-w-[40px]"
        >
          上一页
        </Button>

        <!-- 页码按钮 -->
        {#if total > 0}
          {#each Array.from({ length: Math.min(5, Math.ceil(total / pageSize)) }, (_, i) => {
            // 计算显示哪些页码
            let pageNumbers = [];
            const totalPages = Math.ceil(total / pageSize);

            if (totalPages <= 5) {
              // 如果总页数小于等于5，显示所有页码
              pageNumbers = Array.from({ length: totalPages }, (_, i) => i + 1);
            } else {
              // 否则显示当前页附近的页码
              if (currentPage <= 3) {
                pageNumbers = [1, 2, 3, 4, 5];
              } else if (currentPage >= totalPages - 2) {
                pageNumbers = [totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1, totalPages];
              } else {
                pageNumbers = [currentPage - 2, currentPage - 1, currentPage, currentPage + 1, currentPage + 2];
              }
            }

            return pageNumbers[i];
          }) as page}
            <Button
              variant={page === currentPage ? 'default' : 'outline'}
              size="sm"
              on:click={() => handlePageChange(page)}
              class="h-8 w-8 p-0 font-medium"
            >
              {page}
            </Button>
          {/each}
        {/if}

        <Button
          variant="outline"
          size="sm"
          disabled={currentPage * pageSize >= total}
          on:click={() => handlePageChange(currentPage + 1)}
          class="h-8 px-2 min-w-[40px]"
        >
          下一页
        </Button>
        <Button
          variant="outline"
          size="sm"
          disabled={currentPage * pageSize >= total}
          on:click={() => handlePageChange(Math.ceil(total / pageSize))}
          class="h-8 px-2 min-w-[40px]"
        >
          末页
        </Button>
      </div>
    </div>
  {/if}

  <!-- 删除确认对话框 -->
  {#if showDeleteConfirm && projectToDelete}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full">
        <h3 class="text-xl font-bold mb-4">确认删除项目</h3>
        <p class="mb-4">您确定要删除项目 <span class="font-semibold">{projectToDelete.name}</span> 吗？此操作将删除所有相关数据，且不可恢复。</p>

        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-yellow-700">
                为确认删除，请输入项目简称：<strong>{projectToDelete.name}</strong>
              </p>
            </div>
          </div>
        </div>

        <div class="mb-4">
          <label for="confirmProjectName" class="block text-sm font-medium text-gray-700 mb-1">
            项目简称
          </label>
          <div class="flex gap-2">
            <div class="flex-grow">
              <Input
                id="confirmProjectName"
                type="text"
                placeholder="请输入项目简称以确认删除"
                bind:value={confirmProjectName}
                class={deleteConfirmError ? "border-red-500" : ""}
              />
            </div>
            <Button
              variant="outline"
              size="sm"
              class="whitespace-nowrap"
              onclick={() => confirmProjectName = projectToDelete?.name || ''}
            >
              自动填入
            </Button>
          </div>
          {#if deleteConfirmError}
            <p class="mt-1 text-sm text-red-600">{deleteConfirmError}</p>
            <p class="text-xs text-gray-500">正确的项目简称是: <span class="font-mono bg-gray-100 px-1 rounded">{projectToDelete?.name}</span></p>
          {/if}
        </div>

        <div class="flex justify-end gap-2">
          <Button variant="outline" onclick={cancelDeleteProject} disabled={isDeleting}>
            取消
          </Button>
          <Button variant="destructive" onclick={confirmDeleteProject} disabled={isDeleting || !confirmProjectName}>
            {#if isDeleting}
              <div class="animate-spin h-4 w-4 mr-2 border-2 border-white rounded-full border-t-transparent"></div>
              删除中...
            {:else}
              确认删除
            {/if}
          </Button>
        </div>
      </div>
    </div>
  {/if}

  <!-- 导出项目对话框 -->
  <ExportProjectsDialog bind:open={showExportDialog} projects={projects} />
</div>
