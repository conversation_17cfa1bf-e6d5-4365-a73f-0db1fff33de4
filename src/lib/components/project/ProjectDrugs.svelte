<script lang="ts">
  import { onMount } from 'svelte';
  import { Button } from '$lib/components/ui/button';
  import Input from '$lib/components/ui/input.svelte';
  import Label from '$lib/components/ui/label.svelte';
  import type { ProjectWithDetails, ResearchDrug, DrugGroup } from '$lib/services/projectManagementService';

  // 组件属性
  const props = $props<{projectDetails: ProjectWithDetails}>();
  let projectDetailsData = $derived(props.projectDetails);
  
  console.log('ProjectDrugs组件接收到的项目详情:', projectDetailsData);

  // 状态管理
  let researchDrugs = $state<ResearchDrug[]>([]);
  let drugGroups = $state<DrugGroup[]>([]);
  let drugDialogOpen = $state(false);
  let groupDialogOpen = $state(false);

  // 新药物和分组
  let newDrug = $state('');
  let selectedDrugForGroup = $state<string>(''); // 选择的药物ID或名称
  let newGroupShare = $state(0);

  // 添加安慰剂选项
  const PLACEBO = '安慰剂';

  // localStorage 键名
  const STORAGE_KEY_DRUGS = 'new_project_research_drugs';
  const STORAGE_KEY_GROUPS = 'new_project_drug_groups';

  // 从 localStorage 加载数据
  function loadFromLocalStorage() {
    if (!projectDetailsData || !projectDetailsData.project) {
      console.error('项目详情不完整，无法加载研究药物数据');
      return;
    }
    
    if (projectDetailsData.project.project_id) {
      // 如果项目已有ID，使用项目详情中的数据
      researchDrugs = projectDetailsData.research_drugs || [];
      drugGroups = projectDetailsData.drug_groups || [];
      return;
    }

    try {
      // 尝试从 localStorage 加载数据
      const storedDrugs = localStorage.getItem(STORAGE_KEY_DRUGS);
      const storedGroups = localStorage.getItem(STORAGE_KEY_GROUPS);

      if (storedDrugs) {
        const parsedDrugs = JSON.parse(storedDrugs);
        researchDrugs = parsedDrugs;
        projectDetailsData.research_drugs = parsedDrugs;
      } else {
        researchDrugs = [];
      }

      if (storedGroups) {
        const parsedGroups = JSON.parse(storedGroups);
        drugGroups = parsedGroups;
        projectDetailsData.drug_groups = parsedGroups;
      } else {
        drugGroups = [];
      }

      console.log('从 localStorage 加载数据:', { researchDrugs, drugGroups });
    } catch (error) {
      console.error('从 localStorage 加载数据失败:', error);
      researchDrugs = projectDetailsData.research_drugs || [];
      drugGroups = projectDetailsData.drug_groups || [];
    }
  }

  // 处理数据同步
  $effect(() => {
    if (projectDetailsData?.research_drugs) {
      researchDrugs = projectDetailsData.research_drugs;
    }
    
    if (projectDetailsData?.drug_groups) {
      drugGroups = projectDetailsData.drug_groups;
    }
  });

  // 保存数据到 localStorage
  function saveToLocalStorage() {
    if (!projectDetailsData || !projectDetailsData.project) {
      console.error('项目详情不完整，无法保存研究药物数据');
      return;
    }
    
    if (projectDetailsData.project.project_id) {
      // 如果项目已有ID，不需要保存到 localStorage
      return;
    }

    try {
      localStorage.setItem(STORAGE_KEY_DRUGS, JSON.stringify(researchDrugs));
      localStorage.setItem(STORAGE_KEY_GROUPS, JSON.stringify(drugGroups));
      console.log('保存数据到 localStorage 成功');
    } catch (error) {
      console.error('保存数据到 localStorage 失败:', error);
    }
  }

  // 清除 localStorage 中的数据
  export function clearLocalStorage() {
    localStorage.removeItem(STORAGE_KEY_DRUGS);
    localStorage.removeItem(STORAGE_KEY_GROUPS);
    console.log('清除 localStorage 数据成功');
  }

  // 组件挂载时加载数据
  onMount(() => {
    loadFromLocalStorage();
  });

  // 添加研究药物
  function addResearchDrug() {
    if (!newDrug || !projectDetailsData || !projectDetailsData.project) return;

    console.log('添加研究药物:', newDrug);

    // 创建新研究药物
    const drug: ResearchDrug = {
      project_id: projectDetailsData.project.project_id || '', // 使用空字符串，后端会在保存项目时分配正确的ID
      research_drug: newDrug
    };

    // 添加到列表
    researchDrugs = [...researchDrugs, drug];

    // 更新 projectDetails
    if (Array.isArray(projectDetailsData.research_drugs)) {
      projectDetailsData.research_drugs.push(drug);
    } else {
      projectDetailsData.research_drugs = researchDrugs;
    }

    // 保存到 localStorage
    saveToLocalStorage();

    console.log('更新后的研究药物列表:', researchDrugs);

    // 重置输入并关闭对话框
    newDrug = '';
    drugDialogOpen = false;
  }

  // 删除研究药物
  function removeResearchDrug(index: number) {
    if (!researchDrugs || researchDrugs.length === 0) return;

    console.log('删除研究药物:', { index, drug: researchDrugs[index] });

    // 更新本地状态
    researchDrugs = researchDrugs.filter((_: any, i: number) => i !== index);

    // 更新 projectDetails
    if (projectDetailsData.research_drugs) {
      projectDetailsData.research_drugs = projectDetailsData.research_drugs.filter((_: any, i: number) => i !== index);
    } else {
      projectDetailsData.research_drugs = researchDrugs;
    }

    // 保存到 localStorage
    saveToLocalStorage();

    console.log('删除后的研究药物列表:', researchDrugs);
  }

  // 添加药物分组
  function addDrugGroup() {
    if (!selectedDrugForGroup) return;

    console.log('添加药物分组:', { name: selectedDrugForGroup, share: newGroupShare });

    // 创建新药物分组
    const group: DrugGroup = {
      project_id: projectDetailsData.project.project_id || '', // 使用空字符串，后端会在保存项目时分配正确的ID
      drug_name: selectedDrugForGroup,
      share: newGroupShare
    };

    // 添加到列表
    drugGroups = [...drugGroups, group];

    // 更新 projectDetails
    if (Array.isArray(projectDetailsData.drug_groups)) {
      projectDetailsData.drug_groups.push(group);
    } else {
      projectDetailsData.drug_groups = drugGroups;
    }

    // 保存到 localStorage
    saveToLocalStorage();

    console.log('更新后的药物分组列表:', drugGroups);

    // 重置输入并关闭对话框
    selectedDrugForGroup = '';
    newGroupShare = 0;
    groupDialogOpen = false;
  }

  // 删除药物分组
  function removeDrugGroup(index: number) {
    if (!drugGroups || drugGroups.length === 0) return;

    console.log('删除药物分组:', { index, group: drugGroups[index] });

    // 更新本地状态
    drugGroups = drugGroups.filter((_: any, i: number) => i !== index);

    // 更新 projectDetails
    if (projectDetailsData.drug_groups) {
      projectDetailsData.drug_groups = projectDetailsData.drug_groups.filter((_: any, i: number) => i !== index);
    } else {
      projectDetailsData.drug_groups = drugGroups;
    }

    // 保存到 localStorage
    saveToLocalStorage();

    console.log('删除后的药物分组列表:', drugGroups);
  }
</script>

<div class="space-y-10">
  <!-- 研究药物 -->
  <div>
    <div class="flex justify-between items-center mb-6">
      <div>
        <h2 class="text-xl font-semibold">研究药物</h2>
        <p class="text-sm text-gray-500 mt-1">添加项目中使用的研究药物</p>
      </div>

      <Button on:click={() => drugDialogOpen = true} variant="outline" class="gap-2">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
        添加研究药物
      </Button>

      {#if drugDialogOpen}
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div class="bg-white p-6 rounded-lg shadow-lg w-full max-w-md">
            <h3 class="text-lg font-semibold mb-4">添加研究药物</h3>
            <p class="text-gray-500 text-sm mb-4">请输入研究药物的名称</p>

            <div class="py-4">
              <Label forAttr="research_drug">药物名称</Label>
              <Input
                id="research_drug"
                bind:value={newDrug}
                placeholder="请输入药物名称"
              />
            </div>

            <div class="flex justify-end gap-2">
              <Button on:click={() => drugDialogOpen = false} variant="outline">取消</Button>
              <Button on:click={addResearchDrug} disabled={!newDrug}>添加</Button>
            </div>
          </div>
        </div>
      {/if}
    </div>

    {#if !researchDrugs || researchDrugs.length === 0}
      <div class="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
        <div class="flex justify-center mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="text-gray-400"><circle cx="12" cy="12" r="10"></circle><path d="M12 8v8"></path><path d="M8 12h8"></path></svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无研究药物数据</h3>
        <p class="text-gray-500 mb-4">点击"添加研究药物"按钮来添加项目的研究药物</p>
        <Button on:click={() => drugDialogOpen = true} variant="outline" class="gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
          添加研究药物
        </Button>
      </div>
    {:else}
      <div class="bg-white border rounded-lg overflow-hidden">
        <table class="w-full border-collapse">
          <thead>
            <tr class="bg-gray-50">
              <th class="text-left py-3 px-4 font-medium text-gray-700">药物名称</th>
              <th class="w-[100px] text-right py-3 px-4 font-medium text-gray-700">操作</th>
            </tr>
          </thead>
          <tbody>
            {#each researchDrugs as drug, index}
              <tr class="border-t hover:bg-gray-50">
                <td class="py-3 px-4">{drug.research_drug}</td>
                <td class="text-right py-3 px-4">
                  <Button variant="ghost" size="sm" on:click={() => removeResearchDrug(index)} class="text-red-500 hover:text-red-700">
                    删除
                  </Button>
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    {/if}
  </div>

  <!-- 药物分组 -->
  <div>
    <div class="flex justify-between items-center mb-6">
      <div>
        <h2 class="text-xl font-semibold">药物分组</h2>
        <p class="text-sm text-gray-500 mt-1">添加项目中的药物分组信息</p>
      </div>

      <Button on:click={() => groupDialogOpen = true} variant="outline" class="gap-2">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
        添加药物分组
      </Button>

      {#if groupDialogOpen}
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div class="bg-white p-6 rounded-lg shadow-lg w-full max-w-md">
            <h3 class="text-lg font-semibold mb-4">添加药物分组</h3>
            <p class="text-gray-500 text-sm mb-4">请选择药物并输入份额</p>

            <div class="py-4 space-y-4">
              <div>
                <Label forAttr="drug_name">药物名称</Label>
                <select
                  id="drug_name"
                  bind:value={selectedDrugForGroup}
                  class="h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                >
                  <option value="">请选择药物</option>
                  {#if researchDrugs && researchDrugs.length > 0}
                    {#each researchDrugs as drug}
                      <option value={drug.research_drug}>{drug.research_drug}</option>
                    {/each}
                  {/if}
                  <option value={PLACEBO}>{PLACEBO}</option>
                </select>
                <p class="text-xs text-gray-500 mt-1">选择已添加的研究药物或安慰剂</p>
              </div>
              <div>
                <Label forAttr="share">份额</Label>
                <Input
                  id="share"
                  type="number"
                  min="0"
                  bind:value={newGroupShare}
                  placeholder="请输入份额"
                />
                <p class="text-xs text-gray-500 mt-1">份额表示该药物在项目中的占比</p>
              </div>
            </div>

            <div class="flex justify-end gap-2">
              <Button on:click={() => groupDialogOpen = false} variant="outline">取消</Button>
              <Button on:click={addDrugGroup} disabled={!selectedDrugForGroup}>添加</Button>
            </div>
          </div>
        </div>
      {/if}
    </div>

    {#if !drugGroups || drugGroups.length === 0}
      <div class="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
        <div class="flex justify-center mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="text-gray-400"><path d="M3 6h18"></path><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无药物分组数据</h3>
        <p class="text-gray-500 mb-4">点击"添加药物分组"按钮来添加项目的药物分组信息</p>
        <Button on:click={() => groupDialogOpen = true} variant="outline" class="gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
          添加药物分组
        </Button>
      </div>
    {:else}
      <div class="bg-white border rounded-lg overflow-hidden">
        <table class="w-full border-collapse">
          <thead>
            <tr class="bg-gray-50">
              <th class="text-left py-3 px-4 font-medium text-gray-700">药物名称</th>
              <th class="text-left py-3 px-4 font-medium text-gray-700">份额</th>
              <th class="w-[100px] text-right py-3 px-4 font-medium text-gray-700">操作</th>
            </tr>
          </thead>
          <tbody>
            {#each drugGroups as group, index}
              <tr class="border-t hover:bg-gray-50">
                <td class="py-3 px-4">{group.drug_name}</td>
                <td class="py-3 px-4">{group.share}</td>
                <td class="text-right py-3 px-4">
                  <Button variant="ghost" size="sm" on:click={() => removeDrugGroup(index)} class="text-red-500 hover:text-red-700">
                    删除
                  </Button>
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    {/if}
  </div>

  <!-- 帮助提示 -->
  <div class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
    <h3 class="text-sm font-medium text-blue-800 mb-2">研究药物信息说明</h3>
    <ul class="list-disc pl-5 text-sm text-blue-700 space-y-1">
      <li>研究药物是指在项目中使用的药物</li>
      <li>药物分组用于对研究药物进行分类和管理</li>
      <li>添加药物分组时，可以选择已添加的研究药物或安慰剂</li>
      <li>份额表示该药物在项目中的占比或使用量</li>
      <li>研究药物信息为可选项，您可以稍后再添加</li>
    </ul>
  </div>
</div>

